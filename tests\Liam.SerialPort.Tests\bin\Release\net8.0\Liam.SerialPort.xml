<?xml version="1.0"?>
<doc>
    <assembly>
        <name><PERSON>.SerialPort</name>
    </assembly>
    <members>
        <member name="T:<PERSON>.SerialPort.Constants.SerialPortConstants">
            <summary>
            串口常量定义
            </summary>
        </member>
        <member name="T:<PERSON>.SerialPort.Constants.SerialPortConstants.Defaults">
            <summary>
            默认配置常量
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.BaudRate">
            <summary>
            默认波特率
            </summary>
        </member>
        <member name="F:<PERSON>.SerialPort.Constants.SerialPortConstants.Defaults.DataBits">
            <summary>
            默认数据位
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.ReadTimeout">
            <summary>
            默认读取超时时间（毫秒）
            </summary>
        </member>
        <member name="F:<PERSON>.SerialPort.Constants.SerialPortConstants.Defaults.WriteTimeout">
            <summary>
            默认写入超时时间（毫秒）
            </summary>
        </member>
        <member name="F:<PERSON>.SerialPort.Constants.SerialPortConstants.Defaults.ReceiveBufferSize">
            <summary>
            默认接收缓冲区大小
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.SendBufferSize">
            <summary>
            默认发送缓冲区大小
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.ConnectionTimeout">
            <summary>
            默认连接超时时间（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.RetryCount">
            <summary>
            默认重试次数
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.RetryInterval">
            <summary>
            默认重试间隔（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.AutoReconnectInterval">
            <summary>
            默认自动重连间隔（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.DeviceMonitorInterval">
            <summary>
            默认设备监控间隔（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.HeartbeatInterval">
            <summary>
            默认心跳间隔（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Defaults.MaxLogSize">
            <summary>
            默认日志最大大小（字节）
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.BaudRates">
            <summary>
            常用波特率
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.BaudRates.All">
            <summary>
            获取所有支持的波特率
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.BaudRates.Common">
            <summary>
            常用波特率
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.ErrorCodes">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.OperationTypes">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.Platform">
            <summary>
            平台相关常量
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Platform.WindowsPortPrefix">
            <summary>
            Windows串口名称前缀
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Platform.LinuxPortPrefixes">
            <summary>
            Linux串口设备路径前缀
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Platform.MacOSPortPrefixes">
            <summary>
            macOS串口设备路径前缀
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Platform.WindowsDeviceRegistryPath">
            <summary>
            设备监控注册表路径（Windows）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Platform.LinuxDevicePath">
            <summary>
            设备监控文件路径（Linux）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Platform.LinuxSysPath">
            <summary>
            系统信息路径（Linux）
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.DataFormats">
            <summary>
            数据格式常量
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.DataFormats.HexSeparators">
            <summary>
            十六进制分隔符
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.DataFormats.NewLines">
            <summary>
            常用换行符
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.DataFormats.Terminators">
            <summary>
            常用终止符
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.Performance">
            <summary>
            性能相关常量
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Performance.MinBufferSize">
            <summary>
            最小缓冲区大小
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Performance.MaxBufferSize">
            <summary>
            最大缓冲区大小
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Performance.DefaultReadChunkSize">
            <summary>
            默认读取块大小
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Performance.MaxReadChunkSize">
            <summary>
            最大读取块大小
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Performance.AsyncDelay">
            <summary>
            异步操作延迟（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Performance.DeviceScanInterval">
            <summary>
            设备扫描间隔（毫秒）
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.Logging">
            <summary>
            日志相关常量
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Logging.Category">
            <summary>
            日志类别
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.Logging.EventIds">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Constants.SerialPortConstants.Validation">
            <summary>
            验证相关常量
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MinBaudRate">
            <summary>
            最小波特率
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MaxBaudRate">
            <summary>
            最大波特率
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MinDataBits">
            <summary>
            最小数据位
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MaxDataBits">
            <summary>
            最大数据位
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MinTimeout">
            <summary>
            最小超时时间（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MaxTimeout">
            <summary>
            最大超时时间（毫秒）
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MaxRetryCount">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Constants.SerialPortConstants.Validation.MaxPortNameLength">
            <summary>
            最大端口名称长度
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Events.SerialPortEventArgs">
            <summary>
            串口事件参数基类
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.SerialPortEventArgs.Timestamp">
            <summary>
            事件发生时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.SerialPortEventArgs.PortName">
            <summary>
            串口名称
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Events.SerialPortEventArgs.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
        </member>
        <member name="T:Liam.SerialPort.Events.ConnectionStatusChangedEventArgs">
            <summary>
            连接状态变化事件参数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.ConnectionStatusChangedEventArgs.OldStatus">
            <summary>
            旧状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.ConnectionStatusChangedEventArgs.NewStatus">
            <summary>
            新状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.ConnectionStatusChangedEventArgs.Reason">
            <summary>
            状态变化原因
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Events.ConnectionStatusChangedEventArgs.#ctor(System.String,Liam.SerialPort.Models.ConnectionStatus,Liam.SerialPort.Models.ConnectionStatus,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="oldStatus">旧状态</param>
            <param name="newStatus">新状态</param>
            <param name="reason">状态变化原因</param>
        </member>
        <member name="T:Liam.SerialPort.Events.DataReceivedEventArgs">
            <summary>
            数据接收事件参数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataReceivedEventArgs.Data">
            <summary>
            接收到的数据
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataReceivedEventArgs.Length">
            <summary>
            数据长度
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataReceivedEventArgs.DataAsString">
            <summary>
            数据的字符串表示（UTF-8编码）
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataReceivedEventArgs.DataAsHex">
            <summary>
            数据的十六进制表示
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Events.DataReceivedEventArgs.#ctor(System.String,System.Byte[])">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="data">接收到的数据</param>
        </member>
        <member name="M:Liam.SerialPort.Events.DataReceivedEventArgs.GetDataAsString(System.Text.Encoding)">
            <summary>
            获取指定编码的字符串表示
            </summary>
            <param name="encoding">字符编码</param>
            <returns>字符串表示</returns>
        </member>
        <member name="T:Liam.SerialPort.Events.DataSentEventArgs">
            <summary>
            数据发送完成事件参数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataSentEventArgs.Data">
            <summary>
            发送的数据
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataSentEventArgs.Length">
            <summary>
            数据长度
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataSentEventArgs.Success">
            <summary>
            发送是否成功
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataSentEventArgs.Duration">
            <summary>
            发送耗时
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DataSentEventArgs.Error">
            <summary>
            错误信息（如果发送失败）
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Events.DataSentEventArgs.#ctor(System.String,System.Byte[],System.Boolean,System.TimeSpan,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="data">发送的数据</param>
            <param name="success">发送是否成功</param>
            <param name="duration">发送耗时</param>
            <param name="error">错误信息</param>
        </member>
        <member name="T:Liam.SerialPort.Events.SerialPortErrorEventArgs">
            <summary>
            串口错误事件参数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.SerialPortErrorEventArgs.ErrorType">
            <summary>
            错误类型
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.SerialPortErrorEventArgs.Message">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.SerialPortErrorEventArgs.Exception">
            <summary>
            异常对象
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.SerialPortErrorEventArgs.IsFatal">
            <summary>
            是否为致命错误
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Events.SerialPortErrorEventArgs.#ctor(System.String,Liam.SerialPort.Events.SerialPortErrorType,System.String,System.Exception,System.Boolean)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="errorType">错误类型</param>
            <param name="message">错误消息</param>
            <param name="exception">异常对象</param>
            <param name="isFatal">是否为致命错误</param>
        </member>
        <member name="T:Liam.SerialPort.Events.DeviceChangedEventArgs">
            <summary>
            设备变化事件参数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DeviceChangedEventArgs.ChangeType">
            <summary>
            变化类型
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DeviceChangedEventArgs.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Events.DeviceChangedEventArgs.Timestamp">
            <summary>
            事件发生时间
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Events.DeviceChangedEventArgs.#ctor(Liam.SerialPort.Events.DeviceChangeType,Liam.SerialPort.Models.SerialPortInfo)">
            <summary>
            构造函数
            </summary>
            <param name="changeType">变化类型</param>
            <param name="deviceInfo">设备信息</param>
        </member>
        <member name="T:Liam.SerialPort.Events.SerialPortErrorType">
            <summary>
            串口错误类型
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Connection">
            <summary>
            连接错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Read">
            <summary>
            读取错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Write">
            <summary>
            写入错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Timeout">
            <summary>
            超时错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.DeviceUnavailable">
            <summary>
            设备不可用
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Configuration">
            <summary>
            配置错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.BufferOverflow">
            <summary>
            缓冲区溢出
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Parity">
            <summary>
            校验错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Frame">
            <summary>
            帧错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Overrun">
            <summary>
            溢出错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.SerialPortErrorType.Unknown">
            <summary>
            未知错误
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Events.DeviceChangeType">
            <summary>
            设备变化类型
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.DeviceChangeType.Inserted">
            <summary>
            设备插入
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.DeviceChangeType.Removed">
            <summary>
            设备移除
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Events.DeviceChangeType.StatusChanged">
            <summary>
            设备状态变化
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Exceptions.SerialPortException">
            <summary>
            串口异常基类
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortException.PortName">
            <summary>
            串口名称
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortException.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="errorCode">错误代码</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="errorCode">错误代码</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.SerialPort.Exceptions.SerialPortConnectionException">
            <summary>
            串口连接异常
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConnectionException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.SerialPort.Exceptions.SerialPortTimeoutException">
            <summary>
            串口超时异常
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortTimeoutException.Timeout">
            <summary>
            超时时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortTimeoutException.OperationType">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortTimeoutException.#ctor(System.TimeSpan,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="timeout">超时时间</param>
            <param name="operationType">操作类型</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortTimeoutException.#ctor(System.String,System.TimeSpan,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="timeout">超时时间</param>
            <param name="operationType">操作类型</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortTimeoutException.#ctor(System.String,System.TimeSpan,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="timeout">超时时间</param>
            <param name="operationType">操作类型</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.SerialPort.Exceptions.SerialPortConfigurationException">
            <summary>
            串口配置异常
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortConfigurationException.ParameterName">
            <summary>
            配置参数名称
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortConfigurationException.ParameterValue">
            <summary>
            配置参数值
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String,System.Object,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="parameterName">配置参数名称</param>
            <param name="parameterValue">配置参数值</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String,System.String,System.Object,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="parameterName">配置参数名称</param>
            <param name="parameterValue">配置参数值</param>
            <param name="message">错误消息</param>
        </member>
        <member name="T:Liam.SerialPort.Exceptions.SerialPortDataException">
            <summary>
            串口数据异常
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Exceptions.SerialPortDataException.DataLength">
            <summary>
            数据长度
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="dataLength">数据长度</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String,System.String,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="message">错误消息</param>
            <param name="dataLength">数据长度</param>
        </member>
        <member name="M:Liam.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String,System.String,System.Int32,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="portName">串口名称</param>
            <param name="message">错误消息</param>
            <param name="dataLength">数据长度</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.SerialPort.Extensions.LiamLoggingExtensions">
            <summary>
            Liam.Logging集成扩展方法
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Extensions.LiamLoggingExtensions.AddSerialPortWithLiamLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Liam.SerialPort.Extensions.SerialPortLoggingOptions})">
            <summary>
            为串口服务配置Liam.Logging日志记录
            </summary>
            <param name="services">服务集合</param>
            <param name="configureLogging">日志配置委托</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.LiamLoggingExtensions.IsLiamLoggingAvailable">
            <summary>
            检查Liam.Logging是否可用
            </summary>
            <returns>是否可用</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.LiamLoggingExtensions.ConvertToLiamLogLevel(Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            转换Microsoft.Extensions.Logging.LogLevel到Liam.Logging.Constants.LogLevel
            </summary>
            <param name="logLevel">Microsoft日志级别</param>
            <returns>Liam日志级别</returns>
        </member>
        <member name="T:Liam.SerialPort.Extensions.SerialPortLoggingOptions">
            <summary>
            串口日志配置选项
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.MinLogLevel">
            <summary>
            最小日志级别
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.EnableConsoleLogging">
            <summary>
            启用控制台日志
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.EnableFileLogging">
            <summary>
            启用文件日志
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.LogFilePath">
            <summary>
            日志文件路径
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.EnableStructuredLogging">
            <summary>
            启用结构化日志
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.EnableAsyncLogging">
            <summary>
            启用异步日志
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.EnableLogRotation">
            <summary>
            启用日志轮转
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.MaxFileSize">
            <summary>
            最大文件大小（字节）
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortLoggingOptions.MaxFiles">
            <summary>
            最大文件数量
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Extensions.SerialPortExtensions">
            <summary>
            串口扩展方法
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.SendHexAsync(Liam.SerialPort.Interfaces.ISerialPortService,System.String,System.Threading.CancellationToken)">
            <summary>
            发送十六进制字符串
            </summary>
            <param name="service">串口服务</param>
            <param name="hexString">十六进制字符串</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.SendLineAsync(Liam.SerialPort.Interfaces.ISerialPortService,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            发送带换行符的字符串
            </summary>
            <param name="service">串口服务</param>
            <param name="data">要发送的字符串</param>
            <param name="newLine">换行符，默认为CRLF</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.SendBatchAsync(Liam.SerialPort.Interfaces.ISerialPortService,System.Collections.Generic.IEnumerable{System.Byte[]},System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            批量发送数据
            </summary>
            <param name="service">串口服务</param>
            <param name="dataList">数据列表</param>
            <param name="interval">发送间隔</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.SendBatchAsync(Liam.SerialPort.Interfaces.ISerialPortService,System.Collections.Generic.IEnumerable{System.String},System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            批量发送字符串
            </summary>
            <param name="service">串口服务</param>
            <param name="dataList">字符串列表</param>
            <param name="interval">发送间隔</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.WaitForDataAsync(Liam.SerialPort.Interfaces.ISerialPortService,System.Byte[],System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            等待特定数据
            </summary>
            <param name="service">串口服务</param>
            <param name="expectedData">期望的数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否接收到期望的数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.WaitForStringAsync(Liam.SerialPort.Interfaces.ISerialPortService,System.String,System.TimeSpan,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            等待特定字符串
            </summary>
            <param name="service">串口服务</param>
            <param name="expectedString">期望的字符串</param>
            <param name="timeout">超时时间</param>
            <param name="encoding">字符编码</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否接收到期望的字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.IsUsbDevice(Liam.SerialPort.Models.SerialPortInfo)">
            <summary>
            检查串口是否为USB设备
            </summary>
            <param name="portInfo">串口信息</param>
            <returns>是否为USB设备</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.IsVirtualPort(Liam.SerialPort.Models.SerialPortInfo)">
            <summary>
            检查串口是否为虚拟端口
            </summary>
            <param name="portInfo">串口信息</param>
            <returns>是否为虚拟端口</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.GetDisplayName(Liam.SerialPort.Models.SerialPortInfo)">
            <summary>
            获取串口的友好显示名称
            </summary>
            <param name="portInfo">串口信息</param>
            <returns>友好显示名称</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.CreateSettings(System.Int32,System.Int32,System.IO.Ports.StopBits,System.IO.Ports.Parity)">
            <summary>
            创建常用的串口设置
            </summary>
            <param name="baudRate">波特率</param>
            <param name="dataBits">数据位</param>
            <param name="stopBits">停止位</param>
            <param name="parity">校验位</param>
            <returns>串口设置</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.CreateHighSpeedSettings">
            <summary>
            创建高速串口设置
            </summary>
            <returns>高速串口设置</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.CreateLowSpeedSettings">
            <summary>
            创建低速串口设置
            </summary>
            <returns>低速串口设置</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.IsValidBaudRate(System.Int32)">
            <summary>
            验证波特率是否有效
            </summary>
            <param name="baudRate">波特率</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.GetRecommendedBaudRates">
            <summary>
            获取推荐的波特率列表
            </summary>
            <returns>推荐的波特率列表</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.ToHexString(System.Byte[],System.String)">
            <summary>
            将字节数组转换为十六进制字符串
            </summary>
            <param name="bytes">字节数组</param>
            <param name="separator">分隔符</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.HexStringToBytes(System.String)">
            <summary>
            将十六进制字符串转换为字节数组
            </summary>
            <param name="hexString">十六进制字符串</param>
            <returns>字节数组</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.ContainsPattern(System.Byte[],System.Byte[])">
            <summary>
            检查字节数组是否包含指定的模式
            </summary>
            <param name="data">数据</param>
            <param name="pattern">模式</param>
            <returns>是否包含模式</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.FindPattern(System.Byte[],System.Byte[])">
            <summary>
            查找字节数组中指定模式的位置
            </summary>
            <param name="data">数据</param>
            <param name="pattern">模式</param>
            <returns>模式的位置，未找到返回-1</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.CalculateChecksum(System.Byte[],Liam.SerialPort.Extensions.ChecksumType)">
            <summary>
            计算校验和
            </summary>
            <param name="data">数据</param>
            <param name="checksumType">校验和类型</param>
            <returns>校验和</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortExtensions.VerifyChecksum(System.Byte[],Liam.SerialPort.Extensions.ChecksumType)">
            <summary>
            验证校验和
            </summary>
            <param name="data">数据（包含校验和）</param>
            <param name="checksumType">校验和类型</param>
            <returns>校验和是否正确</returns>
        </member>
        <member name="T:Liam.SerialPort.Extensions.ChecksumType">
            <summary>
            校验和类型
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Extensions.ChecksumType.Sum">
            <summary>
            简单求和
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Extensions.ChecksumType.Xor">
            <summary>
            异或校验
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Extensions.ChecksumType.TwosComplement">
            <summary>
            二进制补码
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Extensions.ServiceCollectionExtensions">
            <summary>
            服务集合扩展方法
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ServiceCollectionExtensions.AddSerialPort(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加串口服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ServiceCollectionExtensions.AddSerialPortSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加串口服务（单例模式）
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ServiceCollectionExtensions.AddSerialPortScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加串口服务（作用域模式）
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ServiceCollectionExtensions.AddSerialPortFactory(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加串口服务工厂
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ServiceCollectionExtensions.AddSerialPortPool(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Int32)">
            <summary>
            添加串口服务池
            </summary>
            <param name="services">服务集合</param>
            <param name="poolSize">池大小</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ServiceCollectionExtensions.ConfigureSerialPortLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            配置串口服务日志
            </summary>
            <param name="services">服务集合</param>
            <param name="configureLogging">日志配置委托</param>
            <returns>服务集合</returns>
        </member>
        <member name="T:Liam.SerialPort.Extensions.ISerialPortServicePool">
            <summary>
            串口服务池接口
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ISerialPortServicePool.GetServiceAsync">
            <summary>
            获取可用的串口服务
            </summary>
            <returns>串口服务</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ISerialPortServicePool.ReturnServiceAsync(Liam.SerialPort.Interfaces.ISerialPortService)">
            <summary>
            归还串口服务
            </summary>
            <param name="service">串口服务</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.ISerialPortServicePool.GetStatus">
            <summary>
            获取池状态
            </summary>
            <returns>池状态</returns>
        </member>
        <member name="T:Liam.SerialPort.Extensions.SerialPortServicePool">
            <summary>
            串口服务池实现
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortServicePool.#ctor(Microsoft.Extensions.Logging.ILogger{Liam.SerialPort.Extensions.SerialPortServicePool},Liam.SerialPort.Interfaces.ISerialPortDiscovery,System.Int32,System.IServiceProvider)">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
            <param name="discovery">设备发现服务</param>
            <param name="maxSize">最大池大小</param>
            <param name="serviceProvider">服务提供者</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortServicePool.GetServiceAsync">
            <summary>
            获取可用的串口服务
            </summary>
            <returns>串口服务</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortServicePool.ReturnServiceAsync(Liam.SerialPort.Interfaces.ISerialPortService)">
            <summary>
            归还串口服务
            </summary>
            <param name="service">串口服务</param>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortServicePool.GetStatus">
            <summary>
            获取池状态
            </summary>
            <returns>池状态</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortServicePool.CreateNewService">
            <summary>
            创建新的串口服务
            </summary>
            <returns>串口服务</returns>
        </member>
        <member name="M:Liam.SerialPort.Extensions.SerialPortServicePool.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Extensions.SerialPortPoolStatus">
            <summary>
            串口服务池状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortPoolStatus.MaxSize">
            <summary>
            最大大小
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortPoolStatus.AvailableCount">
            <summary>
            可用数量
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortPoolStatus.UsedCount">
            <summary>
            使用中数量
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortPoolStatus.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortPoolStatus.UsageRate">
            <summary>
            使用率
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Extensions.SerialPortPoolStatus.IsFull">
            <summary>
            是否已满
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Interfaces.ISerialPortConnection">
            <summary>
            串口连接管理接口，提供串口连接的建立、维护和断开功能
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortConnection.Status">
            <summary>
            获取当前连接状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortConnection.Settings">
            <summary>
            获取当前串口设置
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortConnection.PortInfo">
            <summary>
            获取当前连接的串口信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortConnection.IsConnected">
            <summary>
            获取是否已连接
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortConnection.ConnectedAt">
            <summary>
            获取连接建立时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortConnection.LastActivity">
            <summary>
            获取最后活动时间
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortConnection.StatusChanged">
            <summary>
            连接状态变化事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortConnection.ErrorOccurred">
            <summary>
            连接错误事件
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortConnection.ConnectAsync(System.String,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            建立串口连接
            </summary>
            <param name="portName">串口名称</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortConnection.ConnectAsync(Liam.SerialPort.Models.SerialPortInfo,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            建立串口连接
            </summary>
            <param name="portInfo">串口信息</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortConnection.DisconnectAsync(System.Threading.CancellationToken)">
            <summary>
            断开串口连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortConnection.ReconnectAsync(System.Threading.CancellationToken)">
            <summary>
            重新连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>重连是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortConnection.TestConnectionAsync(System.Threading.CancellationToken)">
            <summary>
            测试连接是否正常
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否正常</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortConnection.GetStatistics">
            <summary>
            获取连接统计信息
            </summary>
            <returns>连接统计信息</returns>
        </member>
        <member name="T:Liam.SerialPort.Interfaces.ISerialPortDataHandler">
            <summary>
            串口数据处理接口，提供数据的发送、接收和缓冲管理功能
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortDataHandler.DataReceived">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortDataHandler.DataSent">
            <summary>
            数据发送完成事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ErrorOccurred">
            <summary>
            数据处理错误事件
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortDataHandler.BytesToRead">
            <summary>
            获取接收缓冲区中的字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortDataHandler.BytesToWrite">
            <summary>
            获取发送缓冲区中的字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ReceiveBufferSize">
            <summary>
            获取接收缓冲区大小
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortDataHandler.SendBufferSize">
            <summary>
            获取发送缓冲区大小
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.SendAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            发送数据（字节数组）
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.SendAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            发送数据（字符串）
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.SendHexAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            发送数据（十六进制字符串）
            </summary>
            <param name="hexData">十六进制字符串</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ReadAsync(System.Int32,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            读取数据（字节数组）
            </summary>
            <param name="count">要读取的字节数，-1表示读取所有可用数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ReadStringAsync(System.Text.Encoding,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            读取数据（字符串）
            </summary>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ReadLineAsync(System.Text.Encoding,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            读取一行数据
            </summary>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的行数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.SendAndReceiveAsync(System.Byte[],System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.SendAndReceiveAsync(System.String,System.TimeSpan,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="timeout">超时时间</param>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ClearReceiveBuffer">
            <summary>
            清空接收缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ClearSendBuffer">
            <summary>
            清空发送缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.ClearAllBuffers">
            <summary>
            清空所有缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.StartListening">
            <summary>
            开始数据接收监听
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDataHandler.StopListening">
            <summary>
            停止数据接收监听
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Interfaces.ISerialPortDiscovery">
            <summary>
            串口设备发现接口，提供串口设备的枚举和热插拔检测功能
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortDiscovery.DeviceChanged">
            <summary>
            设备变化事件
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortDiscovery.IsMonitoring">
            <summary>
            获取是否正在监控设备变化
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDiscovery.GetAvailablePortsAsync(System.Threading.CancellationToken)">
            <summary>
            获取所有可用的串口设备
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口设备信息列表</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDiscovery.GetPortInfoAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            获取指定串口的详细信息
            </summary>
            <param name="portName">串口名称</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口详细信息，如果不存在则返回null</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDiscovery.IsPortAvailableAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            检查指定串口是否可用
            </summary>
            <param name="portName">串口名称</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口是否可用</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDiscovery.StartMonitoringAsync(System.Threading.CancellationToken)">
            <summary>
            开始监控设备变化
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDiscovery.StopMonitoringAsync">
            <summary>
            停止监控设备变化
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortDiscovery.RefreshAsync(System.Threading.CancellationToken)">
            <summary>
            刷新设备列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="T:Liam.SerialPort.Interfaces.ISerialPortService">
            <summary>
            串口服务主接口，提供串口通讯的核心功能
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.Status">
            <summary>
            获取当前连接状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.Settings">
            <summary>
            获取当前串口设置
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.CurrentPort">
            <summary>
            获取当前连接的串口信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.IsConnected">
            <summary>
            获取是否已连接
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.AutoReconnectEnabled">
            <summary>
            获取是否启用自动重连
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortService.StatusChanged">
            <summary>
            连接状态变化事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortService.DataReceived">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortService.ErrorOccurred">
            <summary>
            错误发生事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Interfaces.ISerialPortService.DeviceChanged">
            <summary>
            设备热插拔事件
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.GetAvailablePortsAsync(System.Threading.CancellationToken)">
            <summary>
            获取可用的串口列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口信息列表</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.ConnectAsync(System.String,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            连接到指定串口
            </summary>
            <param name="portName">串口名称</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.ConnectAsync(Liam.SerialPort.Models.SerialPortInfo,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            连接到指定串口
            </summary>
            <param name="portInfo">串口信息</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.DisconnectAsync(System.Threading.CancellationToken)">
            <summary>
            断开连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.SendAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            发送数据（字节数组）
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.SendAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            发送数据（字符串）
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.SendAndReceiveAsync(System.Byte[],System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.SendAndReceiveAsync(System.String,System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.ClearReceiveBuffer">
            <summary>
            清空接收缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Interfaces.ISerialPortService.ClearSendBuffer">
            <summary>
            清空发送缓冲区
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.BytesToRead">
            <summary>
            获取接收缓冲区中的字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Interfaces.ISerialPortService.BytesToWrite">
            <summary>
            获取发送缓冲区中的字节数
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Models.ConnectionStatus">
            <summary>
            连接状态枚举
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Disconnected">
            <summary>
            未连接
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Connecting">
            <summary>
            正在连接
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Connected">
            <summary>
            已连接
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Disconnecting">
            <summary>
            正在断开连接
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Error">
            <summary>
            连接错误
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Reconnecting">
            <summary>
            正在重连
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.Timeout">
            <summary>
            连接超时
            </summary>
        </member>
        <member name="F:Liam.SerialPort.Models.ConnectionStatus.DeviceUnavailable">
            <summary>
            设备不可用
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Models.ConnectionStatusExtensions">
            <summary>
            连接状态扩展方法
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.IsConnected(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否为连接状态
            </summary>
            <param name="status">连接状态</param>
            <returns>是否为连接状态</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.IsDisconnected(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否为断开状态
            </summary>
            <param name="status">连接状态</param>
            <returns>是否为断开状态</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.IsTransitioning(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否为过渡状态（正在连接或断开）
            </summary>
            <param name="status">连接状态</param>
            <returns>是否为过渡状态</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.IsError(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否为错误状态
            </summary>
            <param name="status">连接状态</param>
            <returns>是否为错误状态</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.GetDescription(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            获取状态的中文描述
            </summary>
            <param name="status">连接状态</param>
            <returns>中文描述</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.CanConnect(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否可以执行连接操作
            </summary>
            <param name="status">连接状态</param>
            <returns>是否可以连接</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.CanDisconnect(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否可以执行断开操作
            </summary>
            <param name="status">连接状态</param>
            <returns>是否可以断开</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatusExtensions.CanSendData(Liam.SerialPort.Models.ConnectionStatus)">
            <summary>
            检查是否可以发送数据
            </summary>
            <param name="status">连接状态</param>
            <returns>是否可以发送数据</returns>
        </member>
        <member name="T:Liam.SerialPort.Models.ConnectionStatistics">
            <summary>
            连接统计信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.ConnectedAt">
            <summary>
            连接建立时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.Duration">
            <summary>
            连接持续时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.MessagesSent">
            <summary>
            发送消息数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.MessagesReceived">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.ConnectionAttempts">
            <summary>
            连接尝试次数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.ReconnectionCount">
            <summary>
            重连次数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.ErrorCount">
            <summary>
            错误次数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.LastErrorAt">
            <summary>
            最后错误时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ConnectionStatistics.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatistics.Reset">
            <summary>
            重置统计信息
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatistics.RecordSent(System.Int32)">
            <summary>
            记录发送数据
            </summary>
            <param name="byteCount">字节数</param>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatistics.RecordReceived(System.Int32)">
            <summary>
            记录接收数据
            </summary>
            <param name="byteCount">字节数</param>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatistics.RecordConnection">
            <summary>
            记录连接
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatistics.RecordReconnection">
            <summary>
            记录重连
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.ConnectionStatistics.RecordError(System.String)">
            <summary>
            记录错误
            </summary>
            <param name="error">错误信息</param>
        </member>
        <member name="T:Liam.SerialPort.Models.SerialPortInfo">
            <summary>
            串口设备信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.PortName">
            <summary>
            串口名称（如 COM1, /dev/ttyUSB0）
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.Description">
            <summary>
            设备描述
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.VendorId">
            <summary>
            供应商ID
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.SerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.DeviceType">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.IsAvailable">
            <summary>
            是否可用
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.IsInUse">
            <summary>
            是否正在使用
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.DevicePath">
            <summary>
            设备路径（Linux/macOS）
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.FriendlyName">
            <summary>
            友好名称
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.DeviceInstanceId">
            <summary>
            设备实例ID（Windows）
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.HardwareIds">
            <summary>
            硬件ID列表
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.CompatibleIds">
            <summary>
            兼容ID列表
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.Properties">
            <summary>
            设备属性字典
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.DiscoveredAt">
            <summary>
            发现时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.LastChecked">
            <summary>
            最后检查时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.DisplayName">
            <summary>
            获取显示名称
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.UniqueId">
            <summary>
            获取唯一标识符
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.IsUsbDevice">
            <summary>
            检查是否为USB串口设备
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortInfo.IsVirtualPort">
            <summary>
            检查是否为虚拟串口
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortInfo.ToString">
            <summary>
            重写ToString方法
            </summary>
            <returns>字符串表示</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortInfo.Equals(System.Object)">
            <summary>
            重写Equals方法
            </summary>
            <param name="obj">比较对象</param>
            <returns>是否相等</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortInfo.GetHashCode">
            <summary>
            重写GetHashCode方法
            </summary>
            <returns>哈希码</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortInfo.Clone">
            <summary>
            克隆对象
            </summary>
            <returns>克隆的对象</returns>
        </member>
        <member name="T:Liam.SerialPort.Models.SerialPortSettings">
            <summary>
            串口配置设置
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.BaudRate">
            <summary>
            波特率，默认9600
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.DataBits">
            <summary>
            数据位，默认8位
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.StopBits">
            <summary>
            停止位，默认1位
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.Parity">
            <summary>
            校验位，默认无校验
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.Handshake">
            <summary>
            流控制，默认无流控制
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.ReadTimeout">
            <summary>
            读取超时时间（毫秒），默认5000ms
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.WriteTimeout">
            <summary>
            写入超时时间（毫秒），默认5000ms
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.ReceiveBufferSize">
            <summary>
            接收缓冲区大小，默认4096字节
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.SendBufferSize">
            <summary>
            发送缓冲区大小，默认2048字节
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.DtrEnable">
            <summary>
            是否启用DTR（数据终端就绪）信号
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.RtsEnable">
            <summary>
            是否启用RTS（请求发送）信号
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.NewLine">
            <summary>
            换行符，默认为\r\n
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.Encoding">
            <summary>
            字符编码，默认为UTF-8
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.DiscardNull">
            <summary>
            是否丢弃空字节
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.RetryCount">
            <summary>
            连接重试次数，默认3次
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.RetryInterval">
            <summary>
            连接重试间隔（毫秒），默认1000ms
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.AutoReconnect">
            <summary>
            是否启用自动重连
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.AutoReconnectInterval">
            <summary>
            自动重连间隔（毫秒），默认5000ms
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.MaxAutoReconnectAttempts">
            <summary>
            最大自动重连次数，-1表示无限制
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.ConnectionTimeout">
            <summary>
            连接超时时间（毫秒），默认10000ms
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.HeartbeatInterval">
            <summary>
            心跳检测间隔（毫秒），0表示禁用心跳检测
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.HeartbeatData">
            <summary>
            心跳数据
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.EnableDataLogging">
            <summary>
            是否启用数据日志记录
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.MaxLogSize">
            <summary>
            数据日志最大大小（字节），默认1MB
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.Default">
            <summary>
            创建默认设置
            </summary>
            <returns>默认串口设置</returns>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.Baud9600">
            <summary>
            创建常用的9600波特率设置
            </summary>
            <returns>9600波特率设置</returns>
        </member>
        <member name="P:Liam.SerialPort.Models.SerialPortSettings.Baud115200">
            <summary>
            创建常用的115200波特率设置
            </summary>
            <returns>115200波特率设置</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortSettings.Validate">
            <summary>
            验证设置的有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortSettings.Clone">
            <summary>
            克隆设置对象
            </summary>
            <returns>克隆的设置对象</returns>
        </member>
        <member name="M:Liam.SerialPort.Models.SerialPortSettings.ToString">
            <summary>
            重写ToString方法
            </summary>
            <returns>字符串表示</returns>
        </member>
        <member name="T:Liam.SerialPort.Models.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Models.ValidationResult.Errors">
            <summary>
            错误信息列表
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Models.ValidationResult.#ctor(System.Boolean,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            构造函数
            </summary>
            <param name="isValid">是否有效</param>
            <param name="errors">错误信息列表</param>
        </member>
        <member name="T:Liam.SerialPort.Services.SerialPortConnection">
            <summary>
            串口连接管理服务实现
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortConnection.Status">
            <summary>
            获取当前连接状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortConnection.Settings">
            <summary>
            获取当前串口设置
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortConnection.PortInfo">
            <summary>
            获取当前连接的串口信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortConnection.IsConnected">
            <summary>
            获取是否已连接
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortConnection.ConnectedAt">
            <summary>
            获取连接建立时间
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortConnection.LastActivity">
            <summary>
            获取最后活动时间
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortConnection.StatusChanged">
            <summary>
            连接状态变化事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortConnection.ErrorOccurred">
            <summary>
            连接错误事件
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.#ctor(Microsoft.Extensions.Logging.ILogger{Liam.SerialPort.Services.SerialPortConnection})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.ConnectAsync(System.String,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            建立串口连接
            </summary>
            <param name="portName">串口名称</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.ConnectAsync(Liam.SerialPort.Models.SerialPortInfo,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            建立串口连接
            </summary>
            <param name="portInfo">串口信息</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.DisconnectAsync(System.Threading.CancellationToken)">
            <summary>
            断开串口连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.ReconnectAsync(System.Threading.CancellationToken)">
            <summary>
            重新连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>重连是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.TestConnectionAsync(System.Threading.CancellationToken)">
            <summary>
            测试连接是否正常
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否正常</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.GetStatistics">
            <summary>
            获取连接统计信息
            </summary>
            <returns>连接统计信息</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.GetSerialPort">
            <summary>
            获取串口对象（内部使用）
            </summary>
            <returns>串口对象</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.ChangeStatus(Liam.SerialPort.Models.ConnectionStatus,System.String)">
            <summary>
            改变连接状态
            </summary>
            <param name="newStatus">新状态</param>
            <param name="reason">状态变化原因</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.OnStatusChanged(Liam.SerialPort.Events.ConnectionStatusChangedEventArgs)">
            <summary>
            触发状态变化事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.OnErrorOccurred(Liam.SerialPort.Events.SerialPortErrorEventArgs)">
            <summary>
            触发错误事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.StartAutoReconnect">
            <summary>
            启动自动重连
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.StartHeartbeat">
            <summary>
            启动心跳检测
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.StopTimers">
            <summary>
            停止所有定时器
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.CleanupSerialPort">
            <summary>
            清理串口资源
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortConnection.DisposeAsync">
            <summary>
            异步释放资源
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Services.SerialPortDataHandler">
            <summary>
            串口数据处理服务实现
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortDataHandler.DataReceived">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortDataHandler.DataSent">
            <summary>
            数据发送完成事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortDataHandler.ErrorOccurred">
            <summary>
            数据处理错误事件
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortDataHandler.BytesToRead">
            <summary>
            获取接收缓冲区中的字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortDataHandler.BytesToWrite">
            <summary>
            获取发送缓冲区中的字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortDataHandler.ReceiveBufferSize">
            <summary>
            获取接收缓冲区大小
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortDataHandler.SendBufferSize">
            <summary>
            获取发送缓冲区大小
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.#ctor(Microsoft.Extensions.Logging.ILogger{Liam.SerialPort.Services.SerialPortDataHandler})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.SetSerialPort(System.IO.Ports.SerialPort,System.String)">
            <summary>
            设置串口对象
            </summary>
            <param name="serialPort">串口对象</param>
            <param name="portName">串口名称</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.SendAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            发送数据（字节数组）
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.SendAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            发送数据（字符串）
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.SendHexAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            发送数据（十六进制字符串）
            </summary>
            <param name="hexData">十六进制字符串</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.ReadAsync(System.Int32,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            读取数据（字节数组）
            </summary>
            <param name="count">要读取的字节数，-1表示读取所有可用数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.ReadStringAsync(System.Text.Encoding,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            读取数据（字符串）
            </summary>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.ReadLineAsync(System.Text.Encoding,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            读取一行数据
            </summary>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的行数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.SendAndReceiveAsync(System.Byte[],System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.SendAndReceiveAsync(System.String,System.TimeSpan,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="timeout">超时时间</param>
            <param name="encoding">字符编码，默认为UTF-8</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.ClearReceiveBuffer">
            <summary>
            清空接收缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.ClearSendBuffer">
            <summary>
            清空发送缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.ClearAllBuffers">
            <summary>
            清空所有缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.StartListening">
            <summary>
            开始数据接收监听
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.StopListening">
            <summary>
            停止数据接收监听
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.OnSerialPortDataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
            <summary>
            串口数据接收事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.OnSerialPortErrorReceived(System.Object,System.IO.Ports.SerialErrorReceivedEventArgs)">
            <summary>
            串口错误事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.OnDataReceived(Liam.SerialPort.Events.DataReceivedEventArgs)">
            <summary>
            触发数据接收事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.OnDataSent(Liam.SerialPort.Events.DataSentEventArgs)">
            <summary>
            触发数据发送事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.OnErrorOccurred(Liam.SerialPort.Events.SerialPortErrorEventArgs)">
            <summary>
            触发错误事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDataHandler.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Services.SerialPortDiscovery">
            <summary>
            串口设备发现服务实现
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortDiscovery.DeviceChanged">
            <summary>
            设备变化事件
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortDiscovery.IsMonitoring">
            <summary>
            获取是否正在监控设备变化
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.#ctor(Microsoft.Extensions.Logging.ILogger{Liam.SerialPort.Services.SerialPortDiscovery})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.GetAvailablePortsAsync(System.Threading.CancellationToken)">
            <summary>
            获取所有可用的串口设备
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口设备信息列表</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.GetPortInfoAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            获取指定串口的详细信息
            </summary>
            <param name="portName">串口名称</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口详细信息，如果不存在则返回null</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.IsPortAvailableAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            检查指定串口是否可用
            </summary>
            <param name="portName">串口名称</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口是否可用</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.StartMonitoringAsync(System.Threading.CancellationToken)">
            <summary>
            开始监控设备变化
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.StopMonitoringAsync">
            <summary>
            停止监控设备变化
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.RefreshAsync(System.Threading.CancellationToken)">
            <summary>
            刷新设备列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.MonitorDeviceChanges(System.Object)">
            <summary>
            监控设备变化的定时器回调
            </summary>
            <param name="state">状态对象</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.CheckForDeviceChanges(System.Collections.Generic.IEnumerable{Liam.SerialPort.Models.SerialPortInfo})">
            <summary>
            检查设备变化
            </summary>
            <param name="currentPorts">当前设备列表</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.OnDeviceChanged(Liam.SerialPort.Events.DeviceChangedEventArgs)">
            <summary>
            触发设备变化事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.EnhancePortInfo(Liam.SerialPort.Models.SerialPortInfo,System.Threading.CancellationToken)">
            <summary>
            增强端口信息
            </summary>
            <param name="portInfo">端口信息</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.EnhancePortInfoForWindows(System.Collections.Generic.List{Liam.SerialPort.Models.SerialPortInfo},System.Threading.CancellationToken)">
            <summary>
            Windows平台增强端口信息
            </summary>
            <param name="ports">端口列表</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.EnhancePortInfoForLinux(System.Collections.Generic.List{Liam.SerialPort.Models.SerialPortInfo},System.Threading.CancellationToken)">
            <summary>
            Linux平台增强端口信息
            </summary>
            <param name="ports">端口列表</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.EnhancePortInfoForMacOS(System.Collections.Generic.List{Liam.SerialPort.Models.SerialPortInfo},System.Threading.CancellationToken)">
            <summary>
            macOS平台增强端口信息
            </summary>
            <param name="ports">端口列表</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortDiscovery.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:Liam.SerialPort.Services.SerialPortService">
            <summary>
            串口服务主实现类
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.Status">
            <summary>
            获取当前连接状态
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.Settings">
            <summary>
            获取当前串口设置
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.CurrentPort">
            <summary>
            获取当前连接的串口信息
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.IsConnected">
            <summary>
            获取是否已连接
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.AutoReconnectEnabled">
            <summary>
            获取或设置是否启用自动重连
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.BytesToRead">
            <summary>
            获取接收缓冲区中的字节数
            </summary>
        </member>
        <member name="P:Liam.SerialPort.Services.SerialPortService.BytesToWrite">
            <summary>
            获取发送缓冲区中的字节数
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortService.StatusChanged">
            <summary>
            连接状态变化事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortService.DataReceived">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortService.ErrorOccurred">
            <summary>
            错误发生事件
            </summary>
        </member>
        <member name="E:Liam.SerialPort.Services.SerialPortService.DeviceChanged">
            <summary>
            设备热插拔事件
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.#ctor(Microsoft.Extensions.Logging.ILogger{Liam.SerialPort.Services.SerialPortService},Liam.SerialPort.Interfaces.ISerialPortDiscovery,Liam.SerialPort.Interfaces.ISerialPortConnection,Liam.SerialPort.Interfaces.ISerialPortDataHandler)">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
            <param name="discovery">设备发现服务</param>
            <param name="connection">连接管理服务</param>
            <param name="dataHandler">数据处理服务</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.GetAvailablePortsAsync(System.Threading.CancellationToken)">
            <summary>
            获取可用的串口列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>串口信息列表</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.ConnectAsync(System.String,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            连接到指定串口
            </summary>
            <param name="portName">串口名称</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.ConnectAsync(Liam.SerialPort.Models.SerialPortInfo,Liam.SerialPort.Models.SerialPortSettings,System.Threading.CancellationToken)">
            <summary>
            连接到指定串口
            </summary>
            <param name="portInfo">串口信息</param>
            <param name="settings">串口设置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>连接是否成功</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.DisconnectAsync(System.Threading.CancellationToken)">
            <summary>
            断开连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.SendAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            发送数据（字节数组）
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.SendAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            发送数据（字符串）
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.SendAndReceiveAsync(System.Byte[],System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的数据</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应数据</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.SendAndReceiveAsync(System.String,System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            发送数据并等待响应
            </summary>
            <param name="data">要发送的字符串</param>
            <param name="timeout">超时时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>接收到的响应字符串</returns>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.ClearReceiveBuffer">
            <summary>
            清空接收缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.ClearSendBuffer">
            <summary>
            清空发送缓冲区
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.OnDeviceChanged(System.Object,Liam.SerialPort.Events.DeviceChangedEventArgs)">
            <summary>
            设备变化事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.OnStatusChanged(System.Object,Liam.SerialPort.Events.ConnectionStatusChangedEventArgs)">
            <summary>
            连接状态变化事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.OnDataReceived(System.Object,Liam.SerialPort.Events.DataReceivedEventArgs)">
            <summary>
            数据接收事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.OnConnectionError(System.Object,Liam.SerialPort.Events.SerialPortErrorEventArgs)">
            <summary>
            连接错误事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.OnDataHandlerError(System.Object,Liam.SerialPort.Events.SerialPortErrorEventArgs)">
            <summary>
            数据处理错误事件处理
            </summary>
            <param name="sender">事件发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Liam.SerialPort.Services.SerialPortService.DisposeAsync">
            <summary>
            异步释放资源
            </summary>
        </member>
    </members>
</doc>
